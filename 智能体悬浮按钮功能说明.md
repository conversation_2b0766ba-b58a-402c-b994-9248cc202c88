# 智能体悬浮按钮控制功能实现说明

## 功能概述

在核心网工单详情页面（`CommonWoDetail.vue`）中实现了智能体页面嵌套功能的悬浮按钮控制机制，用于临时控制智能体区域的显示和隐藏。

## 实现的功能特性

### 1. 悬浮控制按钮
- **位置**：页面右下角固定位置
- **样式**：圆角按钮，带有渐变背景和阴影效果
- **状态指示**：
  - 隐藏状态：蓝绿渐变背景，显示"智能助手"
  - 显示状态：红橙渐变背景，显示"隐藏助手"
- **视觉反馈**：
  - 悬停效果：按钮上移并增强阴影
  - 状态指示器：右上角小圆点显示当前状态
  - 光泽动画：悬停时的光泽扫过效果

### 2. 智能体区域控制
- **页面级布局**：智能体区域从页面顶部开始显示，覆盖整个页面高度
- **布局适配**：智能体显示时，主页面区域（包括头部）占2/3宽度，智能体区域占1/3宽度
- **固定定位**：智能体区域使用固定定位，位于页面右侧，z-index为999
- **响应式设计**：在小屏幕设备上智能体区域覆盖整个屏幕
- **平滑过渡**：显示/隐藏时的CSS过渡动画效果

### 3. 临时占位内容
- **占位界面**：当没有实际智能体URL时显示美观的占位内容
- **功能预览**：展示智能体将提供的功能（智能问答、故障分析、处理建议）
- **渐变背景**：使用渐变背景增强视觉效果

### 4. 专业类型控制
- **条件启用**：仅在核心网专业工单中启用智能体功能
- **临时测试**：当前为便于测试，临时开启了所有专业类型的支持

## 技术实现细节

### 新增数据属性
```javascript
// 智能体相关数据
isAiAgentAvailable: false, // 智能体功能是否可用
showAiAgent: false, // 是否显示智能体区域
aiAgentLoading: false, // 智能体加载状态
aiAgentError: false, // 智能体加载错误状态
aiAgentUrl: '', // 智能体页面URL
aiAgentConfig: null, // 智能体配置信息
```

### 核心方法
1. **`checkAiAgentAvailability()`**：检查智能体功能可用性
2. **`toggleAiAgent()`**：切换智能体显示状态
3. **`isCoreNetworkProfession()`**：判断是否为核心网专业
4. **`closeAiAgent()`**：关闭智能体（保留配置便于重新打开）

### 布局结构调整
```html
<!-- 页面级智能体布局容器 -->
<div class="page-ai-agent-layout">
  <!-- 主要页面区域 -->
  <div class="main-page-area">
    <head-fixed-layout>
      <!-- 头部和内容 -->
    </head-fixed-layout>
  </div>

  <!-- 页面级智能体区域 -->
  <div class="page-ai-agent-area">
    <!-- 智能体内容 -->
  </div>
</div>
```

### 样式特性
- **页面级布局**：使用 `.page-ai-agent-layout` 作为最外层容器
- **固定定位智能体**：智能体区域使用 `position: fixed`，覆盖整个页面高度
- **悬浮按钮**：固定定位，z-index: 1000，确保在最上层
- **响应式适配**：针对不同屏幕尺寸的适配规则
- **动画效果**：CSS过渡动画和悬停效果
- **占位内容**：渐变背景和功能预览卡片

## 使用说明

### 1. 功能启用条件
- 当前为测试阶段，所有专业类型的工单都会显示悬浮按钮
- 正式环境中应该只在核心网专业（专业类型ID为31）启用

### 2. 操作方式
- 点击右下角的悬浮按钮可以切换智能体区域的显示/隐藏
- 智能体区域右上角的关闭按钮也可以隐藏智能体区域
- 按钮文字和颜色会根据当前状态动态变化

### 3. 临时性说明
- 当前实现是临时控制机制，便于开发和测试阶段使用
- 占位内容模拟了智能体的功能预览
- 后续可以轻松替换为实际的智能体内容

## 后续优化建议

1. **恢复专业类型限制**：正式环境中应该恢复核心网专业的限制
2. **接口集成**：集成实际的智能体检查和加载接口
3. **用户偏好记忆**：可以考虑记住用户的显示偏好设置
4. **性能优化**：智能体区域的懒加载机制
5. **错误处理**：完善智能体加载失败的处理逻辑

## 文件修改记录

**主要修改文件**：`src/plugin/backbone/modules/commonOrder/CommonWoDetail.vue`

**修改内容**：
- 新增悬浮按钮HTML结构
- 新增智能体占位内容
- 新增数据属性和控制方法
- 新增悬浮按钮和占位内容的CSS样式
- 修改智能体显示控制逻辑

**代码行数**：约200行新增代码（包括HTML、JavaScript和CSS）

## 最新更新（布局调整）

### 布局位置调整
根据用户需求，将智能体区域的显示位置从内容区域内调整为页面级覆盖：

1. **调整前**：智能体区域在 `head-fixed-layout` 内部，仅覆盖内容区域
2. **调整后**：智能体区域在页面最外层，从顶部开始覆盖整个页面高度

### 主要变更
- **HTML结构**：重新组织模板结构，将智能体区域提升到页面级
- **CSS样式**：更新为 `.page-ai-agent-layout` 和 `.page-ai-agent-area`
- **定位方式**：智能体区域使用 `position: fixed` 固定在页面右侧
- **响应式优化**：在小屏幕设备上智能体区域覆盖整个屏幕

### 技术细节
- **z-index**：智能体区域设置为999，确保在页面内容之上
- **高度覆盖**：`height: 100vh` 确保覆盖整个视口高度
- **宽度控制**：保持1/3宽度，主页面区域自动调整为2/3宽度
- **边界处理**：智能体区域右对齐，左侧有分割线
