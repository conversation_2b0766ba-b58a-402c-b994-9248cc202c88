<template>
  <div>
    <el-card
      shadow="always"
      class="card"
      :body-style="{ padding: '10px 24px' }"
    >
      <!--    <el-collapse v-model="activeNames">-->
      <!--      <el-collapse-item name="1">-->
      <div class="header clearfix">
        <span class="header-title">处理详情</span>
      </div>
      <div class="content">
        <el-collapse>
          <el-collapse-item v-for="(item, index) of detailsList" :key="index">
            <span class="collapse-title" slot="title">{{ item.name }}</span>
            <div
              v-for="(itemCont, key) of item.content"
              :key="key"
              class="content__list"
            >
              <span
                class="detail-p"
                v-html="itemCont.handleContent"
                @click="handleBtnClick($event)"
              ></span>
              <template v-if="itemCont.files.length > 0">
                附件：【
                <span style="margin-bottom: 0">
                  <el-tag
                    class="fileName_style"
                    v-for="(itemFile, index) of itemCont.files"
                    @click="previewAppendixFile(item)"
                    v-loading.fullscreen.lock="appendixFileLoading"
                    :title="itemFile.attOrigName"
                    :key="index"
                    ><div class="text-truncate">
                      {{ itemFile.attOrigName }}
                    </div></el-tag
                  > </span
                >】
              </template>
              <template v-if="item.name == '现场打点'">
                <i class="el-icon-place" @click="showMap(itemCont)"></i>
              </template>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <!--      </el-collapse-item>-->
      <!--    </el-collapse>-->
    </el-card>

    <el-dialog
      title="现场打点"
      :visible.sync="mapVisible"
      v-if="mapVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="mapVisible = false"
      width="1000px"
    >
      <map-location
        :woId="woId"
        :process-inst-id="processInstId"
      ></map-location>
    </el-dialog>
    <el-dialog
      title="反馈单详情"
      :visible.sync="feedbackHistoryVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogBackSingleClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <div class="content">
        <el-descriptions title="故障定性信息" class="descriptions" :column="3">
          <template v-if="list.professionalTypeName == '铁塔'">
            <el-descriptions-item label="故障所属专业">{{
              list.professionalTypeName
            }}</el-descriptions-item>
            <el-descriptions-item label="回单人">{{
              list.recoveryPerson
            }}</el-descriptions-item>
            <el-descriptions-item label="回单人联系电话">{{
              list.recoveryPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="回单时间">{{
              list.faultNoticeTime
            }}</el-descriptions-item>
            <el-descriptions-item label="实时直流电压值">{{
              list.siteOfflineReason
            }}</el-descriptions-item>
            <el-descriptions-item label="铁塔站址资源编码">{{
              list.faultReason
            }}</el-descriptions-item>
            <el-descriptions-item label="站址运维ID">{{
              list.vendor
            }}</el-descriptions-item>
            <el-descriptions-item label="站址名称">{{
              list.eqpType
            }}</el-descriptions-item>
            <el-descriptions-item label="运营商是否购买发电服务">
              {{ list.hardwareFlag }}
            </el-descriptions-item>
            <el-descriptions-item label="是否是发电工单">{{
              list.maintenanceSubject
            }}</el-descriptions-item>
            <el-descriptions-item label="发电开始时间">{{
              list.alarmCreateTime
            }}</el-descriptions-item>
            <el-descriptions-item label="发电结束时间">{{
              list.faultOverTime
            }}</el-descriptions-item>
            <el-descriptions-item label="接单时间">{{
              list.faultRecoveryTime
            }}</el-descriptions-item>
            <el-descriptions-item label="所属地市">{{
              list.mobileFaultDutyId
            }}</el-descriptions-item>
            <el-descriptions-item label="所属区县">{{
              list.boardType
            }}</el-descriptions-item>
            <el-descriptions-item label="运营商共享情况">{{
              list.repairQuality
            }}</el-descriptions-item>
            <el-descriptions-item label="站址服务等级">{{
              list.fdbkQuality
            }}</el-descriptions-item>
            <el-descriptions-item label="是否为免责站址">{{
              list.satisfaction
            }}</el-descriptions-item>
            <el-descriptions-item label="申告工单是否铁塔原因">{{
              list.evaluationOpinion
            }}</el-descriptions-item>
            <el-descriptions-item label="申告工单故障分类">{{
              list.faultSource
            }}</el-descriptions-item>
            <el-descriptions-item label="是否申请减免">{{
              list.diskName
            }}</el-descriptions-item>
            <el-descriptions-item
              label="申请减免原因"
              v-if="list.diskName != '不需减免'"
              >{{ list.interSystem }}</el-descriptions-item
            >
            <el-descriptions-item
              label="申请减免分钟数"
              v-if="list.diskName != '不需减免'"
              >{{ list.effectSystem }}</el-descriptions-item
            >
            <el-descriptions-item label="是否上站">{{
              list.interCircuit
            }}</el-descriptions-item>
            <el-descriptions-item
              label="同行处理人"
              :span="1"
              v-if="list.peerProcessorName != '无'"
            >
              <span class="sheetNo_class" @click="openPeerProcessor">{{
                list.peerProcessorName
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="同行处理人" v-else>
              无
            </el-descriptions-item>
            <el-descriptions-item label="处理结果描述">{{
              list.faultRange
            }}</el-descriptions-item>
            <el-descriptions-item
              label="附件"
              v-if="list.file && list.files.length > 0"
            >
              <el-tag
                v-for="(item, index) in list.files"
                class="fileName_style"
                :key="index"
                @click="previewAppendixFile(item)"
                v-loading.fullscreen.lock="appendixFileLoading"
                :title="item.attOrigName"
                ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
              >
            </el-descriptions-item>
            <el-descriptions-item label="附件" v-else>
              无
            </el-descriptions-item>
          </template>
          <template v-else>
            <el-descriptions-item label="故障所属专业">{{
              list.professionalTypeName
            }}</el-descriptions-item>
            <el-descriptions-item label="故障发生时间">{{
              list.alarmCreateTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障通知时间">{{
              list.faultNoticeTime
            }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复时间">{{
              list.businessRecoveryTime
            }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复历时">{{
              recoveryDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障结束时间">{{
              list.faultOverTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障处理历时">{{
              faultDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障发生地区">
              {{ list.faultRegion }}
            </el-descriptions-item>
            <el-descriptions-item label="故障处理部门">
              {{ list.dept }}
            </el-descriptions-item>
            <el-descriptions-item label="处理人">{{
              list.recoveryPerson
            }}</el-descriptions-item>
            <el-descriptions-item label="挂起历时">{{
              suspendDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障处理净历时">{{
              faultCleanDuration
            }}</el-descriptions-item>

            <el-descriptions-item label="是否影响业务">{{
              list.isEffectBusiness
            }}</el-descriptions-item>
            <el-descriptions-item
              label="影响范围"
              v-if="list.isEffectBusiness == '是'"
            >
              {{ list.effectRange }}
            </el-descriptions-item>
            <el-descriptions-item
              label="是否基站退服"
              v-if="list.professionalTypeName == '无线网'"
              >{{ list.isSiteOffline }}</el-descriptions-item
            >
            <el-descriptions-item
              label="退服原因"
              v-if="
                list.professionalTypeName == '无线网' &&
                list.isSiteOffline == '是'
              "
              >{{ list.siteOfflineReason }}</el-descriptions-item
            >
            <el-descriptions-item
              label="同行处理人"
              :span="1"
              v-if="list.peerProcessorName != '无'"
            >
              <span class="sheetNo_class" @click="openPeerProcessor">{{
                list.peerProcessorName
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="同行处理人" v-else>
              无
            </el-descriptions-item>
            <el-descriptions-item
              label="是否有财产损失"
              v-if="
                list.ifPropertyLoss != null &&
                list.ifPropertyLoss != '' &&
                list.ifPropertyLoss != '无'
              "
              >{{ list.ifPropertyLoss }}</el-descriptions-item
            >
            <el-descriptions-item
              label="保单号"
              v-if="list.ifPropertyLoss == '是'"
              >{{ list.insuranceId }}</el-descriptions-item
            >
            <el-descriptions-item
              label="派单是否准确"
              v-if="
                list.isDispatchAccurate != null &&
                list.isDispatchAccurate != '' &&
                list.isDispatchAccurate != '无'
              "
              >{{ list.isDispatchAccurate }}</el-descriptions-item
            >
          </template>
        </el-descriptions>
        <el-descriptions
          title="故障专业信息"
          class="descriptions"
          v-if="list.professionalTypeName != '铁塔'"
        >
          <!--当故障处理方式=自动恢复，不显示以下字段。-->
          <template v-if="list.solvedType != '自动恢复'">
            <el-descriptions-item label="故障处理方式" :span="1">
              {{ list.solvedType }}</el-descriptions-item
            >
            <!-- 省分云 -->
            <template v-if="list.professionalTypeName == '省分云'">
              <el-descriptions-item label="业务名称">
                {{ list.businessName }}</el-descriptions-item
              >
              <el-descriptions-item label="紧急程度" :span="1">
                {{ list.emergencyLevel }}</el-descriptions-item
              >
              <el-descriptions-item label="故障分类" :span="1">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因" :span="1">
                {{ list.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型">
                {{ list.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="是否硬件故障" :span="1">
                {{ list.hardwareFlag }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称">
                {{ list.eqpName }}</el-descriptions-item
              >
            </template>
            <!-- 增值平台/其他/IT监控 -->
            <template
              v-if="
                list.professionalTypeName == '增值平台' ||
                list.professionalTypeName == '其他' ||
                list.professionalTypeName == 'IT监控'
              "
            >
              <el-descriptions-item label="故障分类" :span="1">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因" :span="1">
                {{ list.faultReason }}</el-descriptions-item
              >
            </template>
            <!-- 接入网 -->
            <template v-if="list.professionalTypeName == '接入网'">
              <el-descriptions-item label="故障分类" :span="1">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因" :span="1">
                {{ list.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item label="故障厂家" :span="1">
                {{ list.vendor }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型">
                {{ list.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称">
                {{ list.eqpName }}</el-descriptions-item
              >
              <el-descriptions-item label="故障定责" :span="1">
                {{ list.mobileFaultDutyId }}</el-descriptions-item
              >
            </template>
            <!-- 传输网/集客 -->
            <template
              v-if="
                list.professionalTypeName == '集客' ||
                list.professionalTypeName == '传输网'
              "
            >
              <el-descriptions-item label="故障状态">
                {{ list.faultStatus }}</el-descriptions-item
              >
              <el-descriptions-item label="网络类型">
                {{ list.networkType }}</el-descriptions-item
              >
              <el-descriptions-item label="故障分类" :span="1">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备类型"
                v-if="list.faultCate != '线路故障'"
              >
                {{ list.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因" :span="1">
                {{ list.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item
                label="板卡类型"
                v-if="list.faultCate != '线路故障'"
                :span="1"
              >
                {{ list.boardType }}</el-descriptions-item
              >
              <el-descriptions-item
                label="光缆名称"
                v-if="list.faultCate == '线路故障'"
                :span="1"
              >
                {{ list.deviceName }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备名称"
                v-if="list.faultCate != '线路故障'"
              >
                {{ list.eqpName }}</el-descriptions-item
              >
              <el-descriptions-item label="故障区间" :span="1">
                {{ list.faultRange }}</el-descriptions-item
              >
              <el-descriptions-item label="维护主体" :span="1">
                {{ list.maintenanceSubject }}</el-descriptions-item
              >
              <el-descriptions-item label="受影响系统" :span="1">
                {{ list.effectSystem }}</el-descriptions-item
              >
              <el-descriptions-item label="受影响电路" :span="1">
                {{ list.effectCircuit }}</el-descriptions-item
              >
              <el-descriptions-item
                label="承载业务系统"
                v-if="list.faultCate != '线路故障'"
                :span="1"
              >
                {{ list.bearerSystem }}</el-descriptions-item
              >
              <el-descriptions-item
                label="故障厂家"
                v-if="list.faultCate != '线路故障'"
                :span="1"
              >
                {{ list.vendor }}</el-descriptions-item
              >
              <el-descriptions-item label="是否进行路由调整" :span="1">
                {{ list.isRouterChange }}</el-descriptions-item
              >
              <el-descriptions-item
                label="路由工单编号"
                v-if="list.isRouterChange == '是'"
                :span="1"
              >
                {{ list.routerChangeJobCode }}</el-descriptions-item
              >
            </template>
            <!-- 无线网 -->
            <template v-if="list.professionalTypeName == '无线网'">
              <el-descriptions-item label="故障分类" :span="1">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因" :span="1">
                {{ list.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item
                label="故障厂家"
                v-if="
                  list.faultCate == '基站设备' || list.faultCate == '传输系统'
                "
                :span="1"
              >
                {{ list.vendor }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型" :span="1">
                {{ list.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称" :span="1">
                {{ list.eqpName }}</el-descriptions-item
              >
              <el-descriptions-item label="维护主体" :span="1">
                {{ list.maintenanceSubject }}</el-descriptions-item
              >
              <el-descriptions-item label="故障定责" :span="1">
                {{ list.mobileFaultDutyId }}</el-descriptions-item
              >
            </template>
            <!-- 核心网/固网/移动数通网/数据网/承载A/承载B/WLAN/动环网/5GC/IP互联网/核心网其他 -->
            <template
              v-if="
                list.professionalTypeName == '核心网' ||
                list.professionalTypeName == '核心网其他' ||
                list.professionalTypeName == '5GC' ||
                list.professionalTypeName == '智能网' ||
                list.professionalTypeName == 'IDC' ||
                list.professionalTypeName == 'vIMS' ||
                list.professionalTypeName == '固网' ||
                list.professionalTypeName == '移动数通网' ||
                list.professionalTypeName == '数据网' ||
                list.professionalTypeName == 'IP互联网' ||
                list.professionalTypeName == 'IP承载A网' ||
                list.professionalTypeName == 'IP承载B网' ||
                list.professionalTypeName == 'WLAN' ||
                list.professionalTypeName == '动环网'
              "
            >
              <el-descriptions-item label="故障分类">
                {{ list.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因">
                {{ list.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item label="故障厂家">
                {{ list.vendor }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型">
                {{ list.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称">
                {{ list.eqpName }}</el-descriptions-item
              >
            </template>

            <el-descriptions-item label="故障原因描述" :span="3">
              {{ list.falutReasonDesc }}</el-descriptions-item
            >
            <el-descriptions-item label="备注" :span="3">
              {{ list.falutComment }}</el-descriptions-item
            >
            <el-descriptions-item
              label="附件"
              :span="3"
              v-if="list.files && list.files.length > 0"
            >
              <el-tag
                v-for="(item, index) in list.files"
                class="fileName_style"
                :key="index"
                @click="previewAppendixFile(item)"
                v-loading.fullscreen.lock="appendixFileLoading"
                :title="item.attOrigName"
                ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
              >
            </el-descriptions-item>
            <el-descriptions-item label="附件" v-else>
              无
            </el-descriptions-item>
          </template>
          <template v-if="list.solvedType == '自动恢复'">
            <el-descriptions-item label="故障处理方式" :span="1">
              {{ list.solvedType }}</el-descriptions-item
            >

            <el-descriptions-item label="故障原因描述" :span="2">
              {{ list.falutReasonDesc }}</el-descriptions-item
            >
          </template>
        </el-descriptions>
      </div>
    </el-dialog>
    <el-dialog
      title="同行处理人"
      :visible.sync="dialogPeerProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="PeerProcessorClose"
      :fullscreen="false"
      width="60%"
      top="5vh"
    >
      <PeerProcessorDetail
        :common="{ woId: woId }"
        :persons="list.peerProcessor"
      ></PeerProcessorDetail>
    </el-dialog>
    <!-- 使用图片预览组件 -->
    <image-preview
      :visible.sync="imagePreviewVisible"
      :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download"
      :use-custom-download="true"
      @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"
    ></image-preview>
  </div>
</template>

<script>
import PeerProcessorDetail from "./PeerProcessorDetail";
import {
  apiFileDownload,
  apiGetProcessInfo,
  apiqueryFeedback,
  apiDownloadAppendixFile,
} from "../api/CommonApi";
import mapLocation from "./MapContainer.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  name: "DealDetails",
  props: {
    woId: String,
  },
  components: { PeerProcessorDetail, mapLocation, ImagePreview },
  data() {
    return {
      detailsList: [],
      appendixFileLoading: false,
      feedbackHistoryVisible: false,
      mapVisible: false,
      processInstId: "",
      list: {},
      listAll: [],
      dialogPeerProcessorVisible: false,
      // dialogQualitativeReviewVisible: false,
      //故障定性
      recoveryDuration: null, //业务恢复历时
      faultDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      faultCleanDuration: null, //故障处理净历时
      attachmentArr: [], //附件
      attachmentVisible: false,
      // 多个省的 定性审核区分workItemId
      appendixFileLoading: false,
      // isUploadReport: null,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    this.getDetailData();
  },
  computed: {},

  methods: {
    openPeerProcessor() {
      this.dialogPeerProcessorVisible = true;
    },

    PeerProcessorClose() {
      this.dialogPeerProcessorVisible = false;
    },

    showTime(val) {
      // debugger
      if (val == 0) {
        return "0";
      }
      if (val == "" || null == val) {
        return "";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },

    getFeedbackData(linkId) {
      let param = {
        linkId: linkId,
        isEdit: 0, //isEdit    0:查看 1:编辑
      };

      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let self = this;
            self.listAll = res?.data ?? [];
            if (self.listAll.length > 0) {
              self.list = self.listAll[0];
              self.recoveryDuration = self.showTime(self.list.recoveryDuration);
              self.faultDuration = self.showTime(self.list.faultDuration);
              self.suspendDuration = self.showTime(self.list.suspendDuration);
              self.faultCleanDuration = self.showTime(
                self.list.faultCleanDuration
              );
              if (self.list.appendix) {
                self.attachmentArr = JSON.parse(self.list.appendix);
              }
              // self.deal();
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    dialogBackSingleClose() {
      this.feedbackHistoryVisible = false;
    },
    handleBtnClick(e) {
      console.log(e);
      if (e.target.id === "btn") {
        const linkId = e.target.dataset.linkid;
        this.getFeedbackData(linkId);
        this.feedbackHistoryVisible = true;
      }
    },

    showMap(val) {
      this.processInstId = val.processInstId;
      this.mapVisible = true;
    },
    getDetailData() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          this.detailsList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
    // 预览附件文件
    previewAppendixFile(data) {
      debugger;
      let fileObj = data?.content[0].files[0];

      // 首先检查文件名是否为图片类型
      const fileName = fileObj.attOrigName.toLowerCase();
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));
      fileObj.name = fileObj.attOrigName;
      fileObj.id = fileObj.attId;
      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(fileObj);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = fileObj;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: 0;
}

::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
.content ::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
.content ::v-deep .content__list {
  padding-top: 10px;
  padding-left: 25px;
}
::v-deep .detail-p {
  display: inline-block;
  padding: 0px 5px;
  margin: 0;
}

.fujian {
  display: flex;
  margin-top: 5px;
  .label {
    width: 100px;
    display: block;
  }
  .links {
    flex: 1;
  }
}
.el-icon-place {
  font-size: 22px;
  color: #b50b14;
}
.sheetNo_class {
  color: #b50b14;
  user-select: unset;
  padding: 0px;
  border-bottom: solid 1px #b50b14;
}
</style>
