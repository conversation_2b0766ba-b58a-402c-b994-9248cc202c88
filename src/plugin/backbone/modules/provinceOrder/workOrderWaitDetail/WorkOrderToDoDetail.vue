<template>
  <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse
            :text="`【省分故障工单】${headInfo.title}`"
            :max-lines="2"
          ></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')"
              >基础信息</el-button
            >
            <el-button type="button" @click="onHeadHandleClick('gjxq')"
              >告警详情</el-button
            >
            <el-button
              type="button"
              :style="getStatusStyle()"
              @click="onHeadHandleClick('fkdxq')"
              >反馈单详情</el-button
            >
            <el-button
              type="button"
              v-if="networkType == '一干'"
              @click="onHeadHandleClick('glzd')"
              >关联诊断</el-button
            >
            <el-button
              type="button"
              v-if="networkType == '国际'"
              @click="onHeadHandleClick('clxq')"
              >处理详情</el-button
            >
            <el-button
              type="button"
              v-if="networkType == '国际'"
              @click="onHeadHandleClick('lcrz')"
              >流程日志</el-button
            >
            <el-dropdown
              @command="onHeadHandleClick"
              class="el-button more-dropdown"
              size="medium"
            >
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <template v-for="(item, i) in headMoreDrops">
                  <template v-if="showRelation">
                    <el-dropdown-item :command="item.command" :key="i">
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                  <template v-else>
                    <el-dropdown-item
                      :command="item.command"
                      :key="i"
                      v-if="item.command != 'glzd'"
                    >
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <template v-for="(item, i) in processButtonArr">
            <el-popover
              v-if="item == '现场打点'"
              :key="i"
              placement="top-start"
              title="提示"
              width="200"
              class="btnleft__group"
              trigger="hover"
              content="请到APP上操作"
            >
              <el-button size="mini" slot="reference">{{ item }}</el-button>
            </el-popover>
            <template v-else>
              <template v-if="item == '转派' && turnFlag == 'Y'"> </template>

              <template v-else-if="item == '处理完成'">
                <template v-if="regularFdbkFlag == 'Y'">
                  <el-button
                    size="mini"
                    type="primary"
                    class="btnleft__group"
                    :key="i"
                    @click="buttonClick(item)"
                    v-loading.fullscreen.lock="processFullscreenLoading"
                    >{{ item }}</el-button
                  >
                </template>
              </template>

              <template v-else-if="!detailData">
                <el-button
                  size="mini"
                  type="primary"
                  class="btnleft__group"
                  :key="i"
                  @click="buttonClick(item)"
                  v-loading.fullscreen.lock="processFullscreenLoading"
                  >{{ item }}</el-button
                >
              </template>
            </template>
          </template>
        </el-col>
      </el-row>
      <el-divider
        direction="horizontal"
        content-position="left"
        class="divider"
      ></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col
            :xs="24"
            :sm="24"
            :md="14"
            :lg="12"
            tag="p"
            class="head-sheetId"
          >
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
            <el-row :gutter="20" type="flex" justify="end">
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="5"
                tag="p"
                class="head-up-down"
              >
                <div>当前处理人</div>
                <div
                  class="sheetNo_class"
                  @click="openPeerProcessor"
                  style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  "
                  :title="headInfo.currentHandler"
                >
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="5"
                tag="p"
                class="head-up-down"
              >
                <div>工单状态</div>
                <div style="font-size: 18px; line-height: 28px">
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'"
              >
                <div>剩余受理时间</div>
                <div
                  class="text-primary"
                  style="
                    white-space: nowrap;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="setAcceptTimeLimit"
                >
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="
                  headInfo.sheetStatus != '待受理' &&
                  headInfo.sheetStatus != '已归档' &&
                  headInfo.sheetStatus != '作废' &&
                  headInfo.sheetStatus != '异常归档'
                "
              >
                <div>剩余处理时间</div>
                <div
                  class="text-primary"
                  style="
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="setHandleTimeLimit"
                >
                  {{ setHandleTimeLimit }}
                </div>
              </el-col>

              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="6"
                tag="p"
                class="head-up-down"
              >
                <div>工单总耗时</div>
                <div
                  class="text-primary"
                  style="
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="headInfo.totalWorkingTime"
                >
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单人: {{ headInfo.buildSingleMan }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.buildSingleTime }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            信息来源: {{ headInfo.sourceInfo }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生时间: {{ headInfo.occurrenceTime }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生地区: {{ headInfo.faultRegion }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            紧急程度: {{ headInfo.emergencyDegree }}
          </el-col>
        </el-row>
      </div>
    </template>

    <base-info
      ref="jcxx"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
      :woId="common.woId"
      :workItemId="common.workItemId"
    />
    <!-- 告警详情 -->
    <alarm-detail ref="gjxq" :woId="common.woId" v-if="showAlarm" />
    <!-- 关联诊断 - 根据接口显示 -->
    <relation-diagnosis
      ref="glzd"
      v-if="showRelation"
      :woId="common.woId"
      :topoUrl="basicWorkOrderData.topoUrl"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <!-- 反馈单详情 -->
    <feedback-sheet
      ref="fkdxq"
      v-if="showFkdxq && common.woId"
      :isShowAudit="isShowQualitativeReviewButton"
      :isShowQualitative="isShowQualitative"
      :common="common"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
      :qualitativeType="qualitativeType"
      @qualitativeReviewSubmit="qualitativeReviewSubmit"
      @qualitativeSubmit="qualitativeSubmit"
      @qualitativeInternationSubmit="qualitativeInternationSubmit"
      @qualitativeReviewInternationSubmit="qualitativeReviewInternationSubmit"
    />
    <!-- 处理详情 -->
    <deal-details
      ref="clxq"
      v-if="showClxq && this.common.woId != ''"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <process-log
      ref="lcrz"
      v-if="showLcrz && this.common.woId != ''"
      :woId="common.woId"
      :LogsData="LogsData"
    />
    <flow-chart
      ref="lct"
      v-if="showLct && this.common.woId != ''"
      :common="common"
    />

    <!-- 悬浮 外链接按钮 -->
    <div class="outer-link">
      <span class="num">{{ messageNum }}</span>
      <span class="icon" @click="handleOuterLink"></span>
      <span class="name" @click="handleOuterLink">chatops</span>
    </div>

    <!-- 弹出框信息 -->
    <el-dialog
      title="消息"
      :visible.sync="chatopsVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseMessage"
      width="1000px"
    >
      <iframe
        :src="rsaEncryptUrl"
        frameborder="0"
        width="100%"
        height="500px"
        scrolling="auto"
        style="border: 1px solid #eee; margin-top: -20px"
      ></iframe>
    </el-dialog>

    <el-dialog
      title="阶段反馈"
      :visible.sync="dialogStageFeedbackVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogStageFeedbackClose"
      width="450px"
    >
      <stage-feedback
        :common="common"
        actionName="阶段反馈"
        @stageBackDialogClose="stageBackDialogCommitClose"
      ></stage-feedback>
    </el-dialog>

    <el-dialog
      title="申请挂起"
      :visible.sync="dialogHangUpVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogHangUpClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="1"
        actionName="申请挂起"
        @closeDialogHangUp="dialogHangUpSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="解挂"
      :visible.sync="dialogSolutionToHangVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="2"
        actionName="解挂"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"
      ></hang-up>
    </el-dialog>
    <el-dialog
      title="挂起审核"
      :visible.sync="dialogPendingReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogPendingReviewClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        actionName="挂起审核"
        @closeDialogPendingReview="dialogPendingReviewSubmitClose"
      ></audit>
    </el-dialog>
    <el-dialog
      title="解挂审核"
      :visible.sync="dialogSolutionToHangAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangAuditClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose"
      ></audit>
    </el-dialog>
    <el-dialog
      title="异常终止"
      :visible.sync="dialogAbendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendClose"
      width="450px"
    >
      <abend
        :common="common"
        @closeDialogAbend="dialogAbendSubmitClose"
      ></abend>
    </el-dialog>
    <el-dialog
      title="异常终止审核"
      :visible.sync="dialogAbendAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendAuditClose"
      width="450px"
    >
      <abend-audit
        :common="common"
        @closeDialogAbendAudit="dialogAbendAuditSubmitClose"
      ></abend-audit>
    </el-dialog>
    <el-dialog
      title="转派"
      :visible.sync="dialogTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogTurnToSendSubmitClose"
        :common="common"
        :type="type"
        actionName="转派"
      ></turn-to-send>
    </el-dialog>
    <el-dialog
      title="定性审核"
      :visible.sync="dialogQualitativeAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogQualitativeAuditClose"
      width="500px"
    >
      <qualitative-audit
        @closeDialogQualitativeAudit="dialogQualitativeAuditSumbitClose"
        :common="common"
        :type="type"
        actionName="定性审核"
      >
      </qualitative-audit>
    </el-dialog>
    <el-dialog
      title="派单评价"
      :visible.sync="dialogOrderEvaluateVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogOrderEvaluateClose"
      width="450px"
    >
      <order-evaluate
        @closeDialogOrderEvaluate="dialogOrderEvaluateSumbitClose"
        :common="common"
        :type="type"
        actionName="派单评价"
      >
      </order-evaluate>
    </el-dialog>
    <el-dialog
      title="消障确认"
      :visible.sync="dialogEliminateFaultsVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogEliminateFaultsClose"
      width="1024px"
      custom-class="detail-dialog"
    >
      <eliminate-faults
        @closeDialogEliminateFaults="dialogEliminateFaultsSubmitClose"
        :common="common"
        :type="type"
        :alarmsData="alarmsData"
        actionName="消障确认"
      >
      </eliminate-faults>
    </el-dialog>
    <el-dialog
      title="故障定性"
      :visible.sync="dialogQualitativeVisible"
      :close-on-click-modal="false"
      @close="dialogQualitativeClose"
      append-to-body
      width="1200px"
    >
      <qualitative
        v-if="Object.keys(basicWorkOrderData).length != 0"
        @closeDialogQualitative="dialogQualitativeSubmitClose"
        :common="common"
        :type="type"
        :qData="basicWorkOrderData"
        actionName="故障定性"
      >
      </qualitative>
    </el-dialog>
    <el-dialog
      title="重新转派"
      :visible.sync="dialogAgainTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAgainTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogAgainTurnToSendSubmitClose"
        :common="common"
        :type="type"
        actionName="重新转派"
      ></turn-to-send>
    </el-dialog>
    <el-dialog
      title="PC现场打点"
      :visible.sync="dialogLocationVisible"
      width="480px"
    >
      <el-form
        ref="locationForm"
        :model="locationForm"
        :rules="locationRules"
        label-width="80px"
      >
        <el-form-item label="经度:">
          <el-input v-model="locationForm.longitude"></el-input>
        </el-form-item>
        <el-form-item label="纬度:">
          <el-input v-model="locationForm.latitude"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogLocationVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitLocationForm('locationForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!--    资源勘误-->
    <el-dialog
      title="资源勘误"
      :visible.sync="dialogZYKWVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogZYKWClose"
      :fullscreen="false"
      width="84%"
      top="5vh"
    >
      <ResourceCorrigendum
        :common="common"
        @rcDialog="dialogZYKWSubmitClose"
      ></ResourceCorrigendum>
    </el-dialog>
    <!--处理人列表-->
    <el-dialog
      title="处理人"
      :visible.sync="dialogPeerProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="PeerProcessorClose"
      :fullscreen="false"
      width="70%"
      top="5vh"
    >
      <PeerProcessorDetail
        :common="common"
        :persons="basicWorkOrderData.operatePersonId"
      ></PeerProcessorDetail>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import ProcessLog from "./components/ProcessLog.vue";
import FlowChart from "./components/FlowChart.vue";
import StageFeedback from "./components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
import Abend from "./components/Abend.vue";
import AbendAudit from "./components/AbendAudit";
import TurnToSend from "./components/TurnToSend.vue";
import QualitativeAudit from "./components/QualitativeAudit.vue";
import OrderEvaluate from "./components/OrderEvaluate.vue";
import EliminateFaults from "./components/EliminateFaults.vue";
import Qualitative from "./components/Qualitative.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import PeerProcessorDetail from "../../commonProvince/workOrderWaitDetail/components/PeerProcessorDetail";
import ResourceCorrigendum from "./components/ResourceCorrigendum";
import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  apiRevoke,
  apiHaveRead,
  apiActionPublic,
  getCurrentTime,
  apiSetRead,
  apiGetRsaEncrypt,
  apiMessageRsaEncrypt,
  apiGetMeassgeNum,
  apiGetWoid,
  apiLocation,
  apiGetRelationDiagnosis,
  apiSyncClearDetail,
} from "./api/CommonApi";
export default {
  name: "WorkOrderToDoDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    RelationDiagnosis,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AbendAudit,
    TurnToSend,
    QualitativeAudit,
    OrderEvaluate,
    EliminateFaults,
    Qualitative,
    TextCollapse,
    PeerProcessorDetail,
    ResourceCorrigendum,
  },
  props: {
    detailData: Object,
  },
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        processingTimeLimit: "",
        processTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
      },
      headMoreDrops: [
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "glzd", title: "关联诊断" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: {},
      LogsData: [],
      dealData: [],
      alarmsData: [],
      messageNum: "", // chatops 未读条数
      isTitleCollapse: false,
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        networkType: null,
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        networkTypeName: null, //网络类型中文名
        professionalTypeName: null, //专业类型中文名
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      showRelation: false,
      showAlarm: false,
      processFullscreenLoading: false,
      isShowQualitativeReviewButton: false,
      isShowQualitative: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,

      //资源勘误
      dialogZYKWVisible: false,
      //处理人
      dialogPeerProcessorVisible: false,
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //类型
      type: "single",
      //定性审核
      dialogQualitativeAuditVisible: false,
      // 派单评价
      dialogOrderEvaluateVisible: false,
      //重新转派
      dialogAgainTurnToSendVisible: false,
      // 消障确认
      dialogEliminateFaultsVisible: false,
      // 故障定性
      dialogQualitativeVisible: false,
      // PC现场打点
      dialogLocationVisible: false,
      //定性类型
      qualitativeType: "",
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      networkType: null,
      turnFlag: null, // 转派了，就不显示转派按钮。'Y'不显示
      regularFdbkFlag: null, // 操作了，阶段反馈，才能处理完成，'Y'显示
      locationFlag: null, //现场打点完了，才显示，'Y'显示
      rsaEncrypt: "", // chatops 未读加密
      rsaEncryptUrl: "", // chatops iframe 路径
      chatopsVisible: false, // chatops 弹出框
      locationForm: {}, // pc现场定位form
      locationRules: {
        longitude: [{ require: true, message: "现场打点经度必填" }],
        latitude: [{ require: true, message: "现场打点纬度必填" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
    setHandleTimeLimit() {
      let time = this.headInfo.processTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
  },
  created() {
    this.route = this.$route;
    this.common.workItemId =
      this.detailData?.workItemId || this.$route.query.workItemId;
    this.common.processInstId =
      this.detailData?.processInstId || this.$route.query.processInstId;
    this.common.woId = this.detailData?.woId || this.$route.query.woId;
    this.common.processDefId =
      this.detailData?.processDefId || this.$route.query.processDefId;
    this.common.processNode =
      this.detailData?.processNode || this.$route.query.processNode;

    this.networkType = this.$route.query.networkType;
    this.professionalType = this.$route.query.professionalType;

    if (this.$route.query.outSystem) {
      let param = {
        woCode: this.$route.query.sheetNo,
      };
      apiGetWoid(param).then(res => {
        this.common.woId = res.data.woId;
        this.common.processInstId = res.data.processInstId;
      });
    }
  },
  mounted() {
    if (this.networkType == "一干") {
      this.headMoreDrops = [
        { command: "glzb", title: "关联重保信息" },
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ];
    } else if (this.networkType == "国际") {
      this.headMoreDrops = [{ command: "lct", title: "流程图" }];
    }
    this.fromPage = this.$route.query.fromPage;

    if (this.fromPage == "待阅工单") {
      this.setReadStatus();
    }
    this.getWorkOrderInfo();
    this.syncClearAlarm();
  },
  methods: {
    syncClearAlarm() {
      // this.syncClearAlarmFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiSyncClearDetail(param)
        .then(res => {
          // if (res.status == "0") {
          //   this.$message.success("同步清除成功");
          // } else {
          //   this.$message.error("同步清除失败");
          // }
          // this.syncClearAlarmFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          // this.$message.error("同步清除失败");
          // this.syncClearAlarmFullscreenLoading = false;
        });
    },

    //显示当前处理人弹框
    openPeerProcessor() {
      this.dialogPeerProcessorVisible = true;
    },

    //关闭当前处理人弹框
    PeerProcessorClose() {
      this.dialogPeerProcessorVisible = false;
    },

    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          processNode: this.common.processNode,
          fromPage: this.fromPage,
        };
        if (sessionStorage.getItem(this.common.woId) == "受理") {
          param.actionName = "受理";
        } else {
          param.actionName = "";
        }
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data ?? [];
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      // 第三方打开时， url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });

      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
        woCode: decodeURI(this.detailData?.sheetNo) || sheetNoUrl,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.mainForm ?? {};
            self.dealData = res?.data?.handleDetails ?? {};
            self.alarmsData = res?.data?.alarms ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;

            self.handleData();
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    handleData() {
      let self = this;
      if (
        Object.prototype.hasOwnProperty.call(
          sessionStorage,
          self.basicWorkOrderData.woId
        )
      ) {
        this.common.actionName = sessionStorage.getItem(
          self.basicWorkOrderData.woId
        );
      }

      if (this.fromPage != "已阅工单" && this.fromPage != "待阅工单") {
        this.getShowButton();
      }

      self.turnFlag = self.basicWorkOrderData?.turnFlag;
      self.regularFdbkFlag = self.basicWorkOrderData?.regularFdbkFlag;
      self.locationFlag = self.basicWorkOrderData?.locationFlag;
      self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;

      self.headInfo.currentHandler = self.basicWorkOrderData.operatePerson; // 当前处理人
      self.headInfo.acceptTimeLimit = self.basicWorkOrderData.remainAcceptTime;
      self.headInfo.processTimeLimit = self.basicWorkOrderData.remainHandleTime;
      self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatusName;
      self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
      self.headInfo.buildSingleDept = self.basicWorkOrderData.senderDeptName;
      self.headInfo.buildSingleTime = self.basicWorkOrderData.sheetCreateTime;
      this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
      self.headInfo.sourceInfo = self.basicWorkOrderData.createTypeName;
      self.headInfo.occurrenceTime = self.basicWorkOrderData.alarmCreateTime;
      self.headInfo.emergencyDegree =
        self.basicWorkOrderData.emergencyLevelName;
      self.headInfo.faultRegion = self.basicWorkOrderData.faultRegion;
      self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
      self.common.failureInformTime = self.basicWorkOrderData.sheetCreateTime;
      // self.common.processNode = self.basicWorkOrderData.processNode;
      self.common.sheetNo = self.basicWorkOrderData.sheetNo;
      self.common.isSender =
        self.basicWorkOrderData.senderName == self.userInfo.realName ? 1 : 0;
      self.common.agentManId = self.basicWorkOrderData.agentManId;
      self.common.agentMan = self.basicWorkOrderData.agentMan;
      self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
      self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
      self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
      self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;

      self.common.copyManId = self.basicWorkOrderData.copyManId;
      self.common.copyMan = self.basicWorkOrderData.copyMan;
      self.common.sheetStatus = self.headInfo.sheetStatus;
      self.common.sheetCreateTime = self.basicWorkOrderData.sheetCreateTime;
      self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
      // 转化枚举值
      self.common.professionalType = self.basicWorkOrderData.professionalType;
      self.common.networkType = self.basicWorkOrderData.networkTypeId;
      self.common.networkTypeName = self.basicWorkOrderData.networkType;
      self.common.hangOver = self.basicWorkOrderData.suspendDuration;
      self.common.faultCauseDescription = self.basicWorkOrderData.ppResult;
      this.showClxq = false;
      this.showLcrz = false;
      this.showLct = false;
      this.showRelation = false;
      this.showAlarm = false;
      this.$nextTick(() => {
        this.showClxq = true;
        this.showLcrz = true;
        this.showLct = true;
        // this.showRelation = true;
        if (
          self.basicWorkOrderData.professionalType == 7 ||
          self.basicWorkOrderData.professionalType == 3
        ) {
          this.showRelation = true;
        } else {
          this.getRelationDiagnosis();
        }
        this.showAlarm = true;
      });
      this.getMessageNum();
      if (
        this.headInfo.sheetStatus == "待定性审核" ||
        this.headInfo.sheetStatus == "已归档" ||
        this.headInfo.sheetStatus == "待评价"
      ) {
        // this.showFkdxq = false;
        this.$nextTick(() => {
          this.showFkdxq = true;
        });
      } else {
        this.showFkdxq = false;
      }
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatusName == "异常归档" ||
        basicData.sheetStatusName == "已归档" ||
        basicData.sheetStatusName == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name) {
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        // case "撤单"://没有撤单
        //   this.dialogRevokeVisible = true;
        //   break;
        case "已阅":
          this.haveRead();
          break;
        case "定性审核":
          this.dialogQualitativeAuditVisible = true;
          break;

        case "资源勘误": //界面已调试，接口调试
          this.dialogZYKWVisible = true;
          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "申请挂起":
          this.dialogHangUpVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核":
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止":
          this.dialogAbendVisible = true;
          break;
        case "异常终止审核":
          this.dialogAbendAuditVisible = true;
          break;
        case "转派":
          this.dialogTurnToSendVisible = true;
          break;
        case "重新转派":
          this.dialogAgainTurnToSendVisible = true;
          break;
        case "处理完成":
          this.handleProcessFinish();
          break;
        case "派单评价":
          this.dialogOrderEvaluateVisible = true;
          break;
        case "消障确认":
          this.dialogEliminateFaultsVisible = true;
          break;
        case "故障定性":
          this.dialogQualitativeVisible = true;
          break;
        case "PC现场打点":
          this.dialogLocationVisible = true;
          break;
      }
    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      sessionStorage.setItem(this.common.woId, "受理");
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInstId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            //根据新生成的workItemId再次调用显示按钮的接口
            // this.getShowButton();
            this.getWorkOrderInfo();
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
    },

    //根据接口判断关联诊断是否显示
    getRelationDiagnosis() {
      let param = {
        woId: this.common.woId,
      };

      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            if (res.data.length > 0) {
              let tmp = res.data[0];
              //当 isPPShow为true 时显示关联诊断，
              // 当 isPPShow为false，isAIShow 为 true时显示上海ai
              // 都为false 则不呈现关联诊断版块；
              if (tmp.isPPShow || tmp.isRoomShow || tmp.isAIShow) {
                this.$nextTick(() => {
                  this.showRelation = true;
                });
              } else {
                self.showRelation = false;
              }
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //定性提交
    qualitativeSubmit(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //国际定性提交
    qualitativeInternationSubmit() {
      this.closeAndTurnAround();
    },
    //国际定性审核提交
    qualitativeReviewInternationSubmit() {
      this.closeAndTurnAround();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.common.workItemId = data.workItemId;
      this.common.processInstId = data.processInstId;
      this.common.processDefId = data.processDefId;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    // 阶段反馈
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    stageBackDialogCommitClose() {
      // this.showClxq = false;
      // this.showLcrz = false;
      // this.$nextTick(() => {
      //   this.showLcrz = true;
      //   this.showClxq = true;
      // });
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.dialogStageFeedbackVisible = false;
    },
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.revokeSubmitLoading = true;
          let param = {
            processNode: this.common.processNode,
            sheetNo: this.common.sheetNo,
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("撤单成功");
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error("撤单失败");
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("撤单失败");
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //资源勘误关闭--
    dialogZYKWClose() {
      this.dialogZYKWVisible = false;
    },
    //资源勘误提交--
    dialogZYKWSubmitClose() {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogZYKWVisible = false;
      // if (val == 1) {
      // this.closeAndTurnAround();
      // }
    },
    //挂起关闭
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.closeAndTurnAround();
    },
    //挂起审核关闭
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //定性审核 关闭
    dialogQualitativeAuditClose() {
      this.dialogQualitativeAuditVisible = false;
    },
    dialogQualitativeAuditSumbitClose() {
      this.dialogQualitativeAuditVisible = false;
      this.closeAndTurnAround();
    },
    // 派单评价 关闭
    dialogOrderEvaluateClose() {
      this.dialogOrderEvaluateVisible = false;
    },
    dialogOrderEvaluateSumbitClose() {
      this.dialogOrderEvaluateVisible = false;
      this.closeAndTurnAround();
    },
    // 消障确认 关闭
    dialogEliminateFaultsClose() {
      this.dialogEliminateFaultsVisible = false;
    },
    dialogEliminateFaultsSubmitClose() {
      this.dialogEliminateFaultsVisible = false;
      this.closeAndTurnAround();
    },
    // 故障定性 关闭
    dialogQualitativeClose() {
      this.dialogQualitativeVisible = false;
    },
    dialogQualitativeSubmitClose() {
      this.dialogQualitativeVisible = false;
      this.closeAndTurnAround();
    },
    //解挂审核关闭
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交
    dialogAbendSubmitClose() {
      this.dialogAbendVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //转派
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转派提交
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    //重新转派
    dialogAgainTurnToSendClose() {
      this.dialogAgainTurnToSendVisible = false;
    },
    dialogAgainTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogAgainTurnToSendVisible = false;
        this.closeAndTurnAround();
      } else {
        this.dialogAgainTurnToSendVisible = false;
      }
    },
    // 处理完成 关闭
    handleProcessFinish() {
      this.$confirm("是否对该工单执行 【处理完成】操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          let params = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: "处理完成",
            auditUser: this.userInfo.userName,
            auditTime: getCurrentTime(Date.now()),
          };
          apiActionPublic(params)
            .then(res => {
              if (res.status == "0") {
                this.$message({
                  type: "success",
                  message: "处理完成!",
                });
                sessionStorage.removeItem(this.common.woId);

                this.closeAndTurnAround();
              } else {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
                return false;
              }
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        })
        .catch(() => {});
    },
    getStatusStyle() {
      if (
        this.common.sheetStatus == "待定性" ||
        this.common.sheetStatus == "待定性审核"
      ) {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    setReadStatus() {
      let params = {
        woId: this.common.woId,
      };
      apiSetRead(params)
        .then(res => {
          console.log(res.msg);
        })
        .catch(error => {});
    },
    // 外连接
    handleOuterLink() {
      let params = {
        userId: this.$store.getters.userInfo.userName,
        sheetNo: this.basicWorkOrderData.sheetNo,
        title: "",
      };

      apiGetRsaEncrypt(params)
        .then(res => {
          if (res.data != "") {
            this.rsaEncrypt = res.data;
            // this.rsaEncryptUrl = "/nfm3/chatOpsWeb/talk-view?" + res.data;
            this.rsaEncryptUrl =
              "http://10.245.0.121:5412/chatOpsWeb/talk-view?appKey=EOMS&params=" +
              res.data;
            this.chatopsVisible = true;
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // chatops 未读条数
    getMessageNum() {
      let groupIds = [];
      groupIds.push(this.headInfo.sheetId);
      let paramsRsa = {
        userId: this.$store.getters.userInfo.userName,
        groupIds: groupIds,
      };
      apiMessageRsaEncrypt(paramsRsa)
        .then(res => {
          if (res.data != "") {
            let paramsChatops = {
              appKey: "EOMS",
              params: res.data,
            };
            apiGetMeassgeNum(paramsChatops)
              .then(resNum => {
                if (resNum.data != "") {
                  this.messageNum = resNum.data[this.headInfo.sheetId];
                  console.log(resNum.data);
                }
              })
              .catch(error => {
                console.log(error.msg);
              });
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // PC现场打点
    submitLocationForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            actionName: "现场打点",
            longitude: this.locationForm.longitude,
            latitude: this.locationForm.latitude,
            manageuser: this.userInfo.userName,
            actualSceneSign: "否",
          };
          apiLocation(params)
            .then(res => {
              this.dialogLocationVisible = false;
              this.$message.success({
                message: "PC现场打点成功！",
              });
            })
            .catch(error => {
              this.$message.error(error.msg);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 消息弹出框关闭之后，未读条数清零
    handleCloseMessage() {
      this.chatopsVisible = false;
      this.messageNum = 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
  }
  .head-handle-wrap {
    text-align: right;
    .more-dropdown {
      padding: 0;
      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }
    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }
  .divider {
    margin: 10px 0 16px;
  }
  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }
  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }
  .head-up-down {
    text-align: center;
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    & > div:first-child {
      line-height: 20px;
      @include themify() {
        color: themed("$--color-text-regular");
      }
    }
    & > div:last-child {
      font-weight: 400;
      font-size: 15px;
      line-height: 24px;
    }
  }
  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}
::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}
.outer-link {
  position: fixed;
  top: 55%;
  right: 15px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }
  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }
  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}
</style>
