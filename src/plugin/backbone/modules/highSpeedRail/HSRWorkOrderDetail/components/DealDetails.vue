<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <!--    <el-collapse v-model="activeNames">-->
    <!--      <el-collapse-item name="1">-->
    <div class="header clearfix">
      <span class="header-title">处理详情</span>
    </div>
    <div class="content">
      <el-collapse>
        <el-collapse-item v-for="(item, index) of detailsList" :key="index">
          <span class="collapse-title" slot="title">{{ item.name }}</span>
          <div
            v-for="(itemCont, key) of item.content"
            :key="key"
            class="content__list"
          >
            <span class="detail-p" v-html="itemCont.handleContent"></span>
            <template v-if="itemCont.files.length > 0">
              附件：【
              <span style="margin-bottom: 0">
                <el-tag
                  class="fileName_style"
                  v-for="(itemFile, index) of itemCont.files"
                  @click="previewAppendixFile(item)"
                  v-loading.fullscreen.lock="appendixFileLoading"
                  :title="itemFile.attOrigName"
                  :key="index"
                  ><div class="text-truncate">
                    {{ itemFile.attOrigName }}
                  </div></el-tag
                > </span
              >】
            </template>
            <template v-if="item.name == '现场打点'">
              <i class="el-icon-place" @click="mapVisible = true"></i>
            </template>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!--      </el-collapse-item>-->
    <!--    </el-collapse>-->
    <el-dialog
      title="现场打点"
      :visible.sync="mapVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1000px"
    >
      <map-location :woId="woId"></map-location>
    </el-dialog>
    <!-- 使用图片预览组件 -->
    <image-preview
      :visible.sync="imagePreviewVisible"
      :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download"
      :use-custom-download="true"
      @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"
    ></image-preview>
  </el-card>
</template>

<script>
import {
  apiFileDownload,
  apiGetProcessInfo,
  apiDownloadAppendixFile,
} from "../api/CommonApi";
import mapLocation from "./MapContainer.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  name: "DealDetails",
  props: {
    woId: String,
  },
  components: { mapLocation, ImagePreview },
  data() {
    return {
      detailsList: [],
      appendixFileLoading: false,
      activeNames: ["1"],
      mapVisible: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    this.getDetailData();
  },
  computed: {},

  methods: {
    getDetailData() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          this.detailsList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
    // 预览附件文件
    previewAppendixFile(data) {
      let fileObj = data?.content[0].files[0];

      // 首先检查文件名是否为图片类型
      const fileName = fileObj.attOrigName.toLowerCase();
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));
      fileObj.name = fileObj.attOrigName;
      fileObj.id = fileObj.attId;
      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(fileObj);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = fileObj;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: 0;
}

::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
.content ::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
.content ::v-deep .content__list {
  padding-top: 10px;
  padding-left: 25px;
}
::v-deep .detail-p {
  display: inline-block;
  padding: 0px 5px;
  margin: 0;
}

.fujian {
  display: flex;
  margin-top: 5px;
  .label {
    width: 100px;
    display: block;
  }
  .links {
    flex: 1;
  }
}
.el-icon-place {
  font-size: 22px;
  color: #b50b14;
}
</style>
